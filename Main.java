interface Keyboard {
    void type();
}

class DellKeyboard implements Keyboard {
    public void type() {
        System.out.println("Typing with Dell Keyboard.");
    }
}

class LenovoKeyboard implements Keyboard {
    public void type() {
        System.out.println("Typing with Lenovo Keyboard.");
    }
}

class Computer {
    public void keyboardUsed(Keyboard keyboard) {
        keyboard.type();
    }
}

public class Main {
    public static void main(String[] args) {
        Keyboard keyboard1 = new DellKeyboard();
        Keyboard keyboard2 = new LenovoKeyboard();
        Computer computer = new Computer();
        computer.keyboardUsed(keyboard1);
        computer.keyboardUsed(keyboard2);
    }
}
